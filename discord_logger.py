import discord
import asyncio
import logging
from datetime import datetime
from database import DatabaseManager
from config import Config

# Настройка логирования
logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL))
logger = logging.getLogger(__name__)

class DiscordLogger(discord.Client):
    def __init__(self):
        intents = discord.Intents.all()  # Все права для полного логирования
        super().__init__(intents=intents)

        self.db = DatabaseManager(Config.DATABASE_PATH)
        self.ready = False
        
    async def on_ready(self):
        """Client ready event"""
        logger.info(f'Client {self.user} connected and ready for logging!')
        logger.info(f'Connected to {len(self.guilds)} servers')

        # Save server and channel information
        await self.sync_guilds_and_channels()
        self.ready = True
        
    async def sync_guilds_and_channels(self):
        """Sync servers and channels with database"""
        logger.info("Syncing servers and channels...")

        for guild in self.guilds:
            # Save server
            icon_url = guild.icon.url if guild.icon else None
            self.db.save_guild(guild.id, guild.name, icon_url)

            # Save channels
            for channel in guild.channels:
                if isinstance(channel, (discord.TextChannel, discord.VoiceChannel,
                                      discord.CategoryChannel, discord.ForumChannel)):
                    topic = getattr(channel, 'topic', None)
                    self.db.save_channel(
                        channel.id,
                        guild.id,
                        channel.name,
                        str(channel.type),
                        topic
                    )

        # Save DM channels
        for dm_channel in self.private_channels:
            if isinstance(dm_channel, discord.DMChannel):
                self.db.save_channel(
                    dm_channel.id,
                    None,  # DM doesn't belong to server
                    f"DM with {dm_channel.recipient.name}",
                    "dm",
                    None
                )

        logger.info("Sync completed")
    
    async def on_guild_join(self, guild):
        """Событие присоединения к новому серверу"""
        logger.info(f"Присоединился к серверу: {guild.name}")
        icon_url = guild.icon.url if guild.icon else None
        self.db.save_guild(guild.id, guild.name, icon_url)
        
        # Сохраняем каналы нового сервера
        for channel in guild.channels:
            if isinstance(channel, (discord.TextChannel, discord.VoiceChannel, 
                                  discord.CategoryChannel, discord.ForumChannel)):
                topic = getattr(channel, 'topic', None)
                self.db.save_channel(
                    channel.id, 
                    guild.id, 
                    channel.name, 
                    str(channel.type),
                    topic
                )
    
    async def on_guild_channel_create(self, channel):
        """Событие создания нового канала"""
        if isinstance(channel, (discord.TextChannel, discord.VoiceChannel, 
                              discord.CategoryChannel, discord.ForumChannel)):
            topic = getattr(channel, 'topic', None)
            self.db.save_channel(
                channel.id,
                channel.guild.id,
                channel.name,
                str(channel.type),
                topic
            )
            logger.info(f"Новый канал: {channel.name} на сервере {channel.guild.name}")
    
    async def on_message(self, message):
        """Событие получения нового сообщения"""
        if not self.ready:
            return
            
        # Сохраняем пользователя
        await self.save_user(message.author)
        
        # Сохраняем сообщение
        await self.save_message(message)
        
        # Логируем
        channel_name = getattr(message.channel, 'name', 'DM')
        guild_name = getattr(message.guild, 'name', 'Direct Message') if message.guild else 'Direct Message'
        logger.info(f"[{guild_name}#{channel_name}] {message.author.name}: {message.content[:50]}...")
    
    async def on_message_edit(self, before, after):
        """Событие редактирования сообщения"""
        if not self.ready or before.content == after.content:
            return
            
        # Обновляем сообщение в базе
        self.db.update_message(after.id, after.content)
        
        channel_name = getattr(after.channel, 'name', 'DM')
        guild_name = getattr(after.guild, 'name', 'Direct Message') if after.guild else 'Direct Message'
        logger.info(f"[{guild_name}#{channel_name}] {after.author.name} отредактировал сообщение")
    
    async def on_message_delete(self, message):
        """Событие удаления сообщения"""
        if not self.ready:
            return
            
        # Отмечаем сообщение как удаленное
        self.db.mark_message_deleted(message.id)
        
        channel_name = getattr(message.channel, 'name', 'DM')
        guild_name = getattr(message.guild, 'name', 'Direct Message') if message.guild else 'Direct Message'
        logger.info(f"[{guild_name}#{channel_name}] Удалено сообщение от {message.author.name}")
    
    async def save_user(self, user):
        """Сохранить пользователя в базу данных"""
        avatar_url = user.avatar.url if user.avatar else None
        display_name = getattr(user, 'display_name', user.name)
        
        self.db.save_user(
            user.id,
            user.name,
            display_name,
            avatar_url,
            user.bot
        )
    
    async def save_message(self, message):
        """Сохранить сообщение в базу данных"""
        # Подготавливаем вложения
        attachments = []
        if message.attachments:
            for attachment in message.attachments:
                attachments.append({
                    'filename': attachment.filename,
                    'url': attachment.url,
                    'size': attachment.size,
                    'content_type': attachment.content_type
                })
        
        # Подготавливаем embeds
        embeds = []
        if message.embeds:
            for embed in message.embeds:
                embed_dict = embed.to_dict()
                embeds.append(embed_dict)
        
        # Определяем тип сообщения
        message_type = 'default'
        if message.type == discord.MessageType.pins_add:
            message_type = 'pin'
        elif message.type == discord.MessageType.new_member:
            message_type = 'join'
        elif message.type == discord.MessageType.premium_guild_subscription:
            message_type = 'boost'
        
        # Проверяем, является ли сообщение ответом
        reply_to = None
        if message.reference and message.reference.message_id:
            reply_to = message.reference.message_id
        
        # Сохраняем в базу
        self.db.save_message(
            message.id,
            message.channel.id,
            message.author.id,
            message.content,
            embeds if embeds else None,
            attachments if attachments else None,
            message.created_at,
            message_type,
            reply_to
        )
    
    async def start_logging(self, token: str):
        """Start logging"""
        try:
            await self.start(token)
        except discord.LoginFailure:
            logger.error("Invalid Discord token!")
            raise
        except Exception as e:
            logger.error(f"Error starting client: {e}")
            raise

async def run_discord_logger():
    """Run Discord logger"""
    try:
        token = Config.get_token()
        logger.info("Starting Discord logger...")

        bot = DiscordLogger()
        await bot.start_logging(token)

    except KeyboardInterrupt:
        logger.info("Stopping logger...")
    except Exception as e:
        logger.error(f"Critical error: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(run_discord_logger())
