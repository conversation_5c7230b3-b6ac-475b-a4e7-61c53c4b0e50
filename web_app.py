from flask import Flask, render_template, request, jsonify, redirect, url_for
import json
from datetime import datetime
from database import DatabaseManager
from config import Config
import os

app = Flask(__name__)
app.secret_key = 'discord_logger_secret_key'

# Инициализация базы данных
db = DatabaseManager(Config.DATABASE_PATH)

@app.route('/')
def index():
    """Главная страница - список серверов и каналов"""
    guilds = db.get_guilds()
    channels = db.get_channels()
    
    # Группируем каналы по серверам
    guilds_with_channels = {}
    dm_channels = []
    
    for guild in guilds:
        guilds_with_channels[guild['id']] = {
            'info': guild,
            'channels': []
        }
    
    for channel in channels:
        if channel['guild_id'] is None:  # DM канал
            dm_channels.append(channel)
        elif channel['guild_id'] in guilds_with_channels:
            guilds_with_channels[channel['guild_id']]['channels'].append(channel)
    
    return render_template('index.html', 
                         guilds_with_channels=guilds_with_channels,
                         dm_channels=dm_channels)

@app.route('/channel/<int:channel_id>')
def view_channel(channel_id):
    """Просмотр сообщений канала"""
    page = request.args.get('page', 1, type=int)
    per_page = 50
    offset = (page - 1) * per_page
    include_deleted = request.args.get('include_deleted', 'true') == 'true'
    
    messages = db.get_messages(channel_id, per_page, offset, include_deleted)
    
    # Получаем информацию о канале
    channels = db.get_channels()
    channel_info = next((c for c in channels if c['id'] == channel_id), None)
    
    if not channel_info:
        return "Канал не найден", 404
    
    # Обратный порядок для отображения (новые сообщения внизу)
    messages.reverse()
    
    return render_template('channel.html',
                         messages=messages,
                         channel=channel_info,
                         page=page,
                         per_page=per_page,
                         include_deleted=include_deleted)

@app.route('/api/messages/<int:channel_id>')
def api_messages(channel_id):
    """API для получения сообщений"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 50, type=int)
    offset = (page - 1) * per_page
    include_deleted = request.args.get('include_deleted', 'true') == 'true'
    
    messages = db.get_messages(channel_id, per_page, offset, include_deleted)
    
    # Форматируем сообщения для JSON
    formatted_messages = []
    for msg in messages:
        formatted_msg = {
            'id': msg['id'],
            'content': msg['content'],
            'username': msg['username'],
            'display_name': msg['display_name'],
            'avatar_url': msg['avatar_url'],
            'timestamp': msg['timestamp'],
            'edited_timestamp': msg['edited_timestamp'],
            'deleted': bool(msg['deleted']),
            'deleted_timestamp': msg['deleted_timestamp'],
            'bot': bool(msg['bot']),
            'embeds': msg['embeds'],
            'attachments': msg['attachments'],
            'message_type': msg['message_type']
        }
        formatted_messages.append(formatted_msg)
    
    return jsonify(formatted_messages)

@app.route('/search')
def search():
    """Поиск сообщений"""
    query = request.args.get('q', '').strip()
    
    if not query:
        return render_template('search.html', messages=[], query='')
    
    messages = db.search_messages(query, 100)
    
    return render_template('search.html', messages=messages, query=query)

@app.route('/api/search')
def api_search():
    """API для поиска сообщений"""
    query = request.args.get('q', '').strip()
    
    if not query:
        return jsonify([])
    
    messages = db.search_messages(query, 100)
    
    # Форматируем для JSON
    formatted_messages = []
    for msg in messages:
        formatted_msg = {
            'id': msg['id'],
            'content': msg['content'],
            'username': msg['username'],
            'display_name': msg['display_name'],
            'avatar_url': msg['avatar_url'],
            'timestamp': msg['timestamp'],
            'channel_name': msg['channel_name'],
            'guild_name': msg['guild_name'],
            'deleted': bool(msg['deleted']),
            'bot': bool(msg['bot'])
        }
        formatted_messages.append(formatted_msg)
    
    return jsonify(formatted_messages)

@app.route('/stats')
def stats():
    """Статистика логирования"""
    import sqlite3
    
    with sqlite3.connect(Config.DATABASE_PATH) as conn:
        cursor = conn.cursor()
        
        # Общая статистика
        cursor.execute('SELECT COUNT(*) FROM messages')
        total_messages = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM messages WHERE deleted = TRUE')
        deleted_messages = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM guilds')
        total_guilds = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM channels')
        total_channels = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM users')
        total_users = cursor.fetchone()[0]
        
        # Топ каналов по активности
        cursor.execute('''
            SELECT c.name, g.name as guild_name, COUNT(m.id) as message_count
            FROM messages m
            JOIN channels c ON m.channel_id = c.id
            LEFT JOIN guilds g ON c.guild_id = g.id
            GROUP BY m.channel_id
            ORDER BY message_count DESC
            LIMIT 10
        ''')
        top_channels = cursor.fetchall()
        
        # Топ пользователей по активности
        cursor.execute('''
            SELECT u.username, u.display_name, COUNT(m.id) as message_count
            FROM messages m
            JOIN users u ON m.user_id = u.id
            WHERE u.bot = FALSE
            GROUP BY m.user_id
            ORDER BY message_count DESC
            LIMIT 10
        ''')
        top_users = cursor.fetchall()
    
    stats_data = {
        'total_messages': total_messages,
        'deleted_messages': deleted_messages,
        'total_guilds': total_guilds,
        'total_channels': total_channels,
        'total_users': total_users,
        'top_channels': top_channels,
        'top_users': top_users
    }
    
    return render_template('stats.html', stats=stats_data)

@app.template_filter('datetime')
def datetime_filter(timestamp):
    """Фильтр для форматирования времени"""
    if isinstance(timestamp, str):
        try:
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        except:
            return timestamp
    else:
        dt = timestamp
    
    return dt.strftime('%d.%m.%Y %H:%M:%S')

@app.template_filter('avatar')
def avatar_filter(avatar_url):
    """Фильтр для аватарок"""
    if avatar_url:
        return avatar_url
    return '/static/default_avatar.png'

def run_web_app():
    """Run web application"""
    print(f"Starting web interface on http://{Config.WEB_HOST}:{Config.WEB_PORT}")
    print("For network access use your computer's IP address")

    app.run(
        host=Config.WEB_HOST,
        port=Config.WEB_PORT,
        debug=Config.WEB_DEBUG,
        threaded=True,
        use_reloader=False  # Disable reloader to avoid threading issues
    )

if __name__ == '__main__':
    run_web_app()
