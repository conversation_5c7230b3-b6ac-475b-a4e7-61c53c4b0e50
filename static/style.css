/* Discord-like theme */
:root {
    --bg-primary: #36393f;
    --bg-secondary: #2f3136;
    --bg-tertiary: #202225;
    --bg-accent: #4f545c;
    --text-primary: #ffffff;
    --text-secondary: #b9bbbe;
    --text-muted: #72767d;
    --accent-color: #5865f2;
    --accent-hover: #4752c4;
    --success-color: #3ba55d;
    --warning-color: #faa61a;
    --danger-color: #ed4245;
    --border-color: #40444b;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    overflow: hidden;
}

.app {
    display: flex;
    height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 300px;
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-tertiary);
}

.sidebar-header h2 {
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.sidebar-header i {
    color: var(--accent-color);
    margin-right: 8px;
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 10px 0;
}

.nav-section {
    padding: 0 10px;
    margin-bottom: 20px;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 4px;
    margin-bottom: 2px;
    transition: all 0.2s;
}

.nav-item:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
}

.nav-item.active {
    background-color: var(--accent-color);
    color: var(--text-primary);
}

.nav-item i {
    margin-right: 10px;
    width: 16px;
}

/* Servers list */
.servers-list {
    padding: 0 10px;
}

.server-section {
    margin-bottom: 15px;
}

.server-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
    font-weight: 600;
    color: var(--text-primary);
}

.server-header:hover {
    background-color: var(--bg-accent);
}

.server-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 10px;
}

.server-icon-placeholder {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    margin-right: 10px;
}

.toggle-icon {
    margin-left: auto;
    transition: transform 0.2s;
    font-size: 12px;
}

.channels-list {
    margin-left: 20px;
    margin-top: 5px;
}

.channel-item {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 4px;
    margin-bottom: 1px;
    transition: all 0.2s;
    font-size: 14px;
}

.channel-item:hover {
    background-color: var(--bg-accent);
    color: var(--text-primary);
}

.channel-item i {
    margin-right: 8px;
    width: 16px;
    font-size: 12px;
}

.dm-channel {
    margin-left: 0;
}

/* Main content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
}

.content-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-primary);
}

.content-header h1 {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
}

.content-body {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

/* Channel header */
.channel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.channel-info h1 {
    display: flex;
    align-items: center;
    font-size: 20px;
}

.channel-info h1 i {
    margin-right: 8px;
    color: var(--text-secondary);
}

.channel-topic {
    color: var(--text-secondary);
    font-size: 14px;
    margin-top: 5px;
}

.channel-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.toggle-deleted label {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    cursor: pointer;
}

.toggle-deleted input {
    margin-right: 8px;
}

/* Messages */
.messages-container {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
    padding-right: 10px;
}

.message {
    display: flex;
    padding: 8px 0;
    margin-bottom: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.message:hover {
    background-color: rgba(255, 255, 255, 0.02);
}

.message.deleted {
    opacity: 0.6;
    background-color: rgba(237, 66, 69, 0.1);
}

.message.bot-message {
    background-color: rgba(88, 101, 242, 0.05);
}

.message-avatar {
    margin-right: 15px;
    flex-shrink: 0;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.avatar-placeholder {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 16px;
}

.message-content {
    flex: 1;
    min-width: 0;
}

.message-header {
    display: flex;
    align-items: baseline;
    margin-bottom: 4px;
    flex-wrap: wrap;
    gap: 8px;
}

.username {
    font-weight: 600;
    color: var(--text-primary);
}

.username.bot {
    color: var(--accent-color);
}

.bot-tag {
    background-color: var(--accent-color);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    margin-left: 5px;
    font-weight: 500;
}

.timestamp {
    color: var(--text-muted);
    font-size: 12px;
}

.edited {
    color: var(--text-muted);
    font-size: 11px;
    font-style: italic;
}

.deleted-tag {
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

.message-text {
    color: var(--text-primary);
    line-height: 1.4;
    word-wrap: break-word;
}

.message-attachments {
    margin-top: 8px;
}

.attachment {
    display: flex;
    align-items: center;
    padding: 8px;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    margin-bottom: 4px;
}

.attachment i {
    margin-right: 8px;
    color: var(--text-secondary);
}

.attachment a {
    color: var(--accent-color);
    text-decoration: none;
}

.attachment a:hover {
    text-decoration: underline;
}

.file-size {
    color: var(--text-muted);
    font-size: 12px;
    margin-left: 8px;
}

.message-embeds {
    margin-top: 8px;
}

.embed {
    border-left: 4px solid var(--accent-color);
    background-color: var(--bg-secondary);
    padding: 12px;
    border-radius: 0 4px 4px 0;
    margin-bottom: 4px;
}

.embed-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.embed-description {
    color: var(--text-secondary);
    line-height: 1.4;
}

.embed-link {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 12px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 20px;
    padding: 20px;
}

.pagination-btn {
    background-color: var(--accent-color);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.2s;
}

.pagination-btn:hover {
    background-color: var(--accent-hover);
}

.pagination-current {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Welcome page */
.welcome-content {
    max-width: 800px;
    margin: 0 auto;
}

.welcome-card, .instructions-card {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 30px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.welcome-card h2 {
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.welcome-card h2 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.welcome-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 25px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background-color: var(--bg-tertiary);
    padding: 20px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
}

.stat-card i {
    font-size: 24px;
    color: var(--accent-color);
    margin-right: 15px;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary);
}

.stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.quick-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    background-color: var(--accent-color);
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.action-btn:hover {
    background-color: var(--accent-hover);
}

.action-btn i {
    margin-right: 8px;
}

.instructions-card h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.instructions-card h3 i {
    margin-right: 10px;
    color: var(--warning-color);
}

.instructions-card ul {
    list-style: none;
    padding-left: 0;
}

.instructions-card li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.instructions-card li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
}

/* No messages */
.no-messages {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.no-messages i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-messages p {
    font-size: 16px;
}

/* Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--bg-accent);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a5f66;
}

/* Search page */
.search-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-form {
    margin-bottom: 30px;
}

.search-input-group {
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-primary);
    font-size: 16px;
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-color);
}

.search-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-btn:hover {
    background-color: var(--accent-hover);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.results-header h3 {
    color: var(--text-primary);
}

.results-count {
    color: var(--text-secondary);
    font-size: 14px;
}

.search-result-message {
    background-color: var(--bg-secondary);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
}

.search-result-message.deleted {
    border-left: 4px solid var(--danger-color);
    background-color: rgba(237, 66, 69, 0.1);
}

.message-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 12px;
    color: var(--text-muted);
}

.channel-info {
    display: flex;
    align-items: center;
}

.guild-name {
    font-weight: 500;
}

.separator {
    margin: 0 5px;
}

.channel-name {
    color: var(--text-secondary);
}

.message-preview {
    margin-bottom: 10px;
}

.message-author {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.author-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    margin-right: 8px;
}

.author-avatar-placeholder {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    margin-right: 8px;
}

.author-name {
    font-weight: 500;
    color: var(--text-primary);
    margin-right: 10px;
}

.deleted-indicator {
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    padding: 2px 4px;
    border-radius: 3px;
    font-weight: 500;
}

.message-content-preview {
    color: var(--text-secondary);
    line-height: 1.4;
}

.message-actions {
    text-align: right;
}

.view-in-channel {
    color: var(--accent-color);
    text-decoration: none;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
}

.view-in-channel:hover {
    text-decoration: underline;
}

.view-in-channel i {
    margin-left: 5px;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.no-results i {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-results h3 {
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.search-tips {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 30px;
    border: 1px solid var(--border-color);
}

.search-tips h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.search-tips h3 i {
    margin-right: 10px;
    color: var(--warning-color);
}

.search-tips ul {
    list-style: none;
    padding-left: 0;
}

.search-tips li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.search-tips li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
}

mark {
    background-color: var(--warning-color);
    color: var(--bg-primary);
    padding: 2px 4px;
    border-radius: 2px;
}

/* Stats page */
.stats-container {
    max-width: 1000px;
    margin: 0 auto;
}

.stats-overview {
    margin-bottom: 40px;
}

.stats-overview h2 {
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.stats-overview h2 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.stats-grid .stat-card.large {
    grid-column: span 2;
}

.stat-card .stat-icon {
    font-size: 32px;
    margin-right: 20px;
    width: 60px;
    text-align: center;
}

.stat-icon.deleted {
    color: var(--danger-color);
}

.stat-content {
    flex: 1;
}

.stat-content .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: var(--text-primary);
    line-height: 1;
}

.stat-content .stat-label {
    color: var(--text-secondary);
    font-size: 16px;
    margin-top: 5px;
}

.stat-percentage {
    color: var(--text-muted);
    font-size: 14px;
    margin-top: 2px;
}

.stats-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.stats-section {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 25px;
    border: 1px solid var(--border-color);
}

.stats-section h3 {
    color: var(--text-primary);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.stats-section h3 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.top-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: var(--bg-tertiary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.item-info {
    flex: 1;
    min-width: 0;
}

.item-name {
    color: var(--text-primary);
    font-weight: 500;
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.item-name i {
    margin-right: 8px;
    color: var(--text-secondary);
    width: 16px;
}

.item-meta {
    color: var(--text-muted);
    font-size: 12px;
}

.guild-name {
    color: var(--text-secondary);
}

.username {
    color: var(--text-muted);
    font-style: italic;
}

.item-count {
    color: var(--accent-color);
    font-weight: 500;
    white-space: nowrap;
}

.stats-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.info-card {
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 25px;
    border: 1px solid var(--border-color);
}

.info-card h3 {
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

.info-card h3 i {
    margin-right: 10px;
    color: var(--accent-color);
}

.info-card ul {
    list-style: none;
    padding-left: 0;
}

.info-card li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.info-card li::before {
    content: '•';
    color: var(--accent-color);
    position: absolute;
    left: 0;
}

.info-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 250px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid .stat-card.large {
        grid-column: span 1;
    }

    .stats-details {
        grid-template-columns: 1fr;
    }

    .stats-info {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
    }

    .search-input-group {
        flex-direction: column;
    }
}
