// Discord Logger JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Инициализация различных компонентов
    initializeMessageFormatting();
    initializeScrollBehavior();
    initializeKeyboardShortcuts();
    initializeTooltips();
}

function initializeMessageFormatting() {
    // Форматирование сообщений
    const messageTexts = document.querySelectorAll('.message-text');
    
    messageTexts.forEach(function(messageText) {
        formatMessageContent(messageText);
    });
}

function formatMessageContent(element) {
    let content = element.innerHTML;
    
    // Форматирование упоминаний пользователей
    content = content.replace(/@(\w+)/g, '<span class="mention user-mention">@$1</span>');
    
    // Форматирование упоминаний каналов
    content = content.replace(/#(\w+)/g, '<span class="mention channel-mention">#$1</span>');
    
    // Форматирование ссылок
    content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="message-link">$1</a>');
    
    // Форматирование Discord эмодзи
    content = content.replace(/<:([\w]+):(\d+)>/g, '<span class="custom-emoji" title="$1">:$1:</span>');
    
    // Форматирование жирного текста
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Форматирование курсива
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Форматирование зачеркнутого текста
    content = content.replace(/~~(.*?)~~/g, '<del>$1</del>');
    
    // Форматирование кода
    content = content.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
    
    // Форматирование блоков кода
    content = content.replace(/```([\s\S]*?)```/g, '<pre class="code-block">$1</pre>');
    
    element.innerHTML = content;
}

function initializeScrollBehavior() {
    // Автоматическая прокрутка к новым сообщениям
    const messagesContainer = document.getElementById('messagesContainer');
    
    if (messagesContainer) {
        // Прокрутка к низу при загрузке первой страницы
        const urlParams = new URLSearchParams(window.location.search);
        const page = parseInt(urlParams.get('page')) || 1;
        
        if (page === 1) {
            setTimeout(() => {
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }, 100);
        }
        
        // Плавная прокрутка к конкретному сообщению
        const hash = window.location.hash;
        if (hash.startsWith('#message-')) {
            const messageId = hash.replace('#message-', '');
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            
            if (messageElement) {
                setTimeout(() => {
                    messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    messageElement.style.backgroundColor = 'rgba(88, 101, 242, 0.2)';
                    
                    setTimeout(() => {
                        messageElement.style.backgroundColor = '';
                    }, 2000);
                }, 100);
            }
        }
    }
}

function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+F для поиска
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
            } else {
                window.location.href = '/search';
            }
        }
        
        // Escape для очистки поиска
        if (e.key === 'Escape') {
            const searchInput = document.querySelector('.search-input');
            if (searchInput && searchInput === document.activeElement) {
                searchInput.value = '';
            }
        }
        
        // Стрелки для навигации по страницам
        if (e.key === 'ArrowLeft' && e.ctrlKey) {
            const prevBtn = document.querySelector('.pagination-btn[href*="page=' + (getCurrentPage() - 1) + '"]');
            if (prevBtn) {
                window.location.href = prevBtn.href;
            }
        }
        
        if (e.key === 'ArrowRight' && e.ctrlKey) {
            const nextBtn = document.querySelector('.pagination-btn[href*="page=' + (getCurrentPage() + 1) + '"]');
            if (nextBtn) {
                window.location.href = nextBtn.href;
            }
        }
    });
}

function getCurrentPage() {
    const urlParams = new URLSearchParams(window.location.search);
    return parseInt(urlParams.get('page')) || 1;
}

function initializeTooltips() {
    // Простые тултипы для различных элементов
    const elementsWithTooltips = document.querySelectorAll('[title]');
    
    elementsWithTooltips.forEach(function(element) {
        element.addEventListener('mouseenter', showTooltip);
        element.addEventListener('mouseleave', hideTooltip);
    });
}

function showTooltip(e) {
    const element = e.target;
    const title = element.getAttribute('title');
    
    if (!title) return;
    
    // Создаем тултип
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = title;
    tooltip.style.position = 'absolute';
    tooltip.style.backgroundColor = 'var(--bg-tertiary)';
    tooltip.style.color = 'var(--text-primary)';
    tooltip.style.padding = '8px 12px';
    tooltip.style.borderRadius = '4px';
    tooltip.style.fontSize = '12px';
    tooltip.style.zIndex = '1000';
    tooltip.style.pointerEvents = 'none';
    tooltip.style.border = '1px solid var(--border-color)';
    tooltip.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.3)';
    
    document.body.appendChild(tooltip);
    
    // Позиционируем тултип
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
    tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    
    // Убираем title чтобы не показывался стандартный тултип
    element.setAttribute('data-original-title', title);
    element.removeAttribute('title');
    
    // Сохраняем ссылку на тултип
    element._tooltip = tooltip;
}

function hideTooltip(e) {
    const element = e.target;
    
    if (element._tooltip) {
        document.body.removeChild(element._tooltip);
        element._tooltip = null;
    }
    
    // Восстанавливаем title
    const originalTitle = element.getAttribute('data-original-title');
    if (originalTitle) {
        element.setAttribute('title', originalTitle);
        element.removeAttribute('data-original-title');
    }
}

// Функции для работы с API
function loadMessages(channelId, page = 1, includeDeleted = true) {
    const url = `/api/messages/${channelId}?page=${page}&include_deleted=${includeDeleted}`;
    
    return fetch(url)
        .then(response => response.json())
        .catch(error => {
            console.error('Ошибка загрузки сообщений:', error);
            return [];
        });
}

function searchMessages(query) {
    const url = `/api/search?q=${encodeURIComponent(query)}`;
    
    return fetch(url)
        .then(response => response.json())
        .catch(error => {
            console.error('Ошибка поиска:', error);
            return [];
        });
}

// Утилиты для работы с датами
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    // Если сообщение сегодня
    if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
    }
    
    // Если сообщение вчера
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
        return 'Вчера в ' + date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
    }
    
    // Если сообщение на этой неделе
    if (diff < 7 * 24 * 60 * 60 * 1000) {
        const days = ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота'];
        return days[date.getDay()] + ' в ' + date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
    }
    
    // Полная дата
    return date.toLocaleDateString('ru-RU') + ' в ' + date.toLocaleTimeString('ru-RU', { hour: '2-digit', minute: '2-digit' });
}

// Функция для копирования текста в буфер обмена
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('Скопировано в буфер обмена');
        });
    } else {
        // Fallback для старых браузеров
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showNotification('Скопировано в буфер обмена');
    }
}

// Простая система уведомлений
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.position = 'fixed';
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '4px';
    notification.style.zIndex = '10000';
    notification.style.fontSize = '14px';
    notification.style.fontWeight = '500';
    
    switch (type) {
        case 'success':
            notification.style.backgroundColor = 'var(--success-color)';
            notification.style.color = 'white';
            break;
        case 'error':
            notification.style.backgroundColor = 'var(--danger-color)';
            notification.style.color = 'white';
            break;
        default:
            notification.style.backgroundColor = 'var(--accent-color)';
            notification.style.color = 'white';
    }
    
    document.body.appendChild(notification);
    
    // Автоматическое скрытие через 3 секунды
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.opacity = '0';
            notification.style.transition = 'opacity 0.3s';
            
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 3000);
}

// Экспорт функций для использования в других скриптах
window.DiscordLogger = {
    loadMessages,
    searchMessages,
    formatTimestamp,
    copyToClipboard,
    showNotification
};
