#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discord Logger - Скрипт установки зависимостей
"""

import subprocess
import sys
import os

def install_requirements():
    """Установка зависимостей из requirements.txt"""
    print("Installing dependencies...")

    try:
        # Проверяем наличие pip
        subprocess.check_call([sys.executable, "-m", "pip", "--version"])
    except subprocess.CalledProcessError:
        print("ERROR: pip not found! Please install pip and try again.")
        return False

    try:
        # Устанавливаем зависимости
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("SUCCESS: Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"ERROR: Failed to install dependencies: {e}")
        return False

def check_python_version():
    """Проверка версии Python"""
    if sys.version_info < (3, 7):
        print("ERROR: Python 3.7 or higher required!")
        print(f"   Current version: {sys.version}")
        return False

    print(f"SUCCESS: Python version: {sys.version}")
    return True

def main():
    """Основная функция установки"""
    print("Discord Logger - Installation")
    print("=" * 40)

    # Проверяем версию Python
    if not check_python_version():
        input("Press Enter to exit...")
        return

    # Устанавливаем зависимости
    if not install_requirements():
        input("Press Enter to exit...")
        return

    print("\nInstallation completed successfully!")
    print("You can now run the application with:")
    print("   python main.py")

    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
