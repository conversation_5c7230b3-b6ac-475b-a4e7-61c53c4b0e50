#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discord Logger - Основной файл запуска

Этот скрипт запускает Discord бота для логирования сообщений
и веб-интерфейс для их просмотра.
"""

import asyncio
import threading
import time
import sys
import os
from datetime import datetime

# Добавляем текущую директорию в путь для импорта модулей
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from discord_logger import run_discord_logger
from web_app import run_web_app
from config import Config
import logging

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('discord_logger.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def print_banner():
    """Вывод баннера приложения"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                        Discord Logger                        ║
║                                                              ║
║  Быстрое логирование сообщений Discord с веб-интерфейсом    ║
║                                                              ║
║  Возможности:                                                ║
║  • Логирование всех сообщений в реальном времени            ║
║  • Сохранение удаленных сообщений                           ║
║  • Веб-интерфейс в стиле Discord                            ║
║  • Поиск по сообщениям                                      ║
║  • Статистика и аналитика                                   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def print_instructions():
    """Вывод инструкций по использованию"""
    instructions = """
📋 ИНСТРУКЦИИ ПО НАСТРОЙКЕ:

1. Создайте Discord приложение:
   • Перейдите на https://discord.com/developers/applications
   • Нажмите "New Application"
   • Дайте название приложению

2. Создайте бота:
   • Перейдите в раздел "Bot"
   • Нажмите "Add Bot"
   • Скопируйте токен бота

3. Настройте права бота:
   • В разделе "Bot" включите все Privileged Gateway Intents
   • В разделе "OAuth2 > URL Generator":
     - Выберите scope: "bot"
     - Выберите права: "Read Messages/View Channels", "Read Message History"

4. Пригласите бота на сервер:
   • Используйте сгенерированную ссылку
   • Выберите нужные серверы

5. Запустите скрипт и введите токен бота

⚠️  ВАЖНО: Токен бота - это секретная информация! Не делитесь им!

🌐 После запуска веб-интерфейс будет доступен по адресу:
   http://localhost:5000 (локально)
   http://[IP_ВАШЕГО_ПК]:5000 (из локальной сети)
"""
    print(instructions)

def run_web_server():
    """Запуск веб-сервера в отдельном потоке"""
    try:
        logger.info("Запуск веб-сервера...")
        run_web_app()
    except Exception as e:
        logger.error(f"Ошибка веб-сервера: {e}")

async def run_discord_bot():
    """Запуск Discord бота"""
    try:
        logger.info("Запуск Discord бота...")
        await run_discord_logger()
    except Exception as e:
        logger.error(f"Ошибка Discord бота: {e}")
        raise

def check_dependencies():
    """Проверка установленных зависимостей"""
    required_packages = [
        'discord.py',
        'flask',
        'aiofiles',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').replace('.py', ''))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Отсутствуют необходимые пакеты: {', '.join(missing_packages)}")
        print("📦 Установите их командой:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def get_local_ip():
    """Получение локального IP адреса"""
    import socket
    try:
        # Подключаемся к внешнему адресу чтобы определить локальный IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "localhost"

def main():
    """Основная функция"""
    print_banner()
    
    # Проверяем зависимости
    if not check_dependencies():
        input("\nНажмите Enter для выхода...")
        return
    
    print_instructions()
    
    # Запрашиваем подтверждение
    response = input("\n🚀 Готовы начать? (y/n): ").strip().lower()
    if response not in ['y', 'yes', 'да', 'д']:
        print("Выход...")
        return
    
    try:
        # Получаем токен
        print("\n🔑 Настройка токена...")
        token = Config.get_token()
        
        if not token:
            print("❌ Токен не может быть пустым!")
            return
        
        print("✅ Токен сохранен")
        
        # Запускаем веб-сервер в отдельном потоке
        print("\n🌐 Запуск веб-интерфейса...")
        web_thread = threading.Thread(target=run_web_server, daemon=True)
        web_thread.start()
        
        # Даем время веб-серверу запуститься
        time.sleep(2)
        
        local_ip = get_local_ip()
        print(f"✅ Веб-интерфейс запущен!")
        print(f"   Локальный доступ: http://localhost:{Config.WEB_PORT}")
        print(f"   Сетевой доступ:   http://{local_ip}:{Config.WEB_PORT}")
        
        # Запускаем Discord бота
        print("\n🤖 Запуск Discord бота...")
        print("   Ожидание подключения...")
        
        # Запускаем бота в основном потоке
        asyncio.run(run_discord_bot())
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Остановка приложения...")
        logger.info("Приложение остановлено пользователем")
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        logger.error(f"Критическая ошибка: {e}", exc_info=True)
        input("\nНажмите Enter для выхода...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Неожиданная ошибка: {e}")
        input("Нажмите Enter для выхода...")
