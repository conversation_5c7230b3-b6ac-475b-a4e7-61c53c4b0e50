#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discord Logger - Основной файл запуска

Этот скрипт запускает Discord бота для логирования сообщений
и веб-интерфейс для их просмотра.
"""

import asyncio
import threading
import time
import sys
import os
from datetime import datetime

# Добавляем текущую директорию в путь для импорта модулей
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from discord_logger import run_discord_logger
from web_app import run_web_app
from config import Config
import logging

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('discord_logger.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def print_banner():
    """Print application banner"""
    banner = """
================================================================
                        Discord Logger

    Fast Discord message logging with web interface

    Features:
    • Real-time message logging
    • Deleted message preservation
    • Discord-style web interface
    • Message search
    • Statistics and analytics

================================================================
    """
    print(banner)

def print_instructions():
    """Print usage instructions"""
    instructions = """
HOW THE PROGRAM WORKS:

This program connects to Discord using your account and logs
all messages you can see. It is NOT a bot.

ADVANTAGES:
• Logs ALL messages you can see
• Saves deleted messages
• Works with all servers you're in
• No admin rights required
• Completely anonymous - no one knows about logging

IMPORTANT NOTES:
• Program uses your personal Discord token
• Token is secret information - don't share it!
• Program works only while running
• All data is saved locally on your computer

After startup, web interface will be available at:
   http://localhost:5000 (local)
   http://[YOUR_IP]:5000 (network)

Web interface can be opened on phone/tablet in the same network!
"""
    print(instructions)

def run_web_server():
    """Run web server in separate thread"""
    try:
        logger.info("Starting web server...")
        run_web_app()
    except Exception as e:
        logger.error(f"Web server error: {e}")

async def run_discord_bot():
    """Run Discord client"""
    try:
        logger.info("Starting Discord client...")
        await run_discord_logger()
    except Exception as e:
        logger.error(f"Discord client error: {e}")
        raise

def check_dependencies():
    """Check installed dependencies"""
    required_packages = [
        'discord.py',
        'flask',
        'aiofiles'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package.replace('-', '_').replace('.py', ''))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"ERROR: Missing required packages: {', '.join(missing_packages)}")
        print("Install them with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    return True

def get_local_ip():
    """Get local IP address"""
    import socket
    try:
        # Connect to external address to determine local IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except:
        return "localhost"

def main():
    """Main function"""
    print_banner()

    # Check dependencies
    if not check_dependencies():
        input("\nPress Enter to exit...")
        return

    print_instructions()

    # Ask for confirmation
    response = input("\nReady to start? (y/n): ").strip().lower()
    if response not in ['y', 'yes']:
        print("Exiting...")
        return
    
    try:
        # Get token
        print("\nToken setup...")
        token = Config.get_token()

        if not token:
            print("ERROR: Token cannot be empty!")
            return

        print("SUCCESS: Token saved")

        # Start web server in separate thread
        print("\nStarting web interface...")
        web_thread = threading.Thread(target=run_web_server, daemon=True)
        web_thread.start()

        # Give web server time to start
        time.sleep(2)

        local_ip = get_local_ip()
        print(f"SUCCESS: Web interface started!")
        print(f"   Local access:   http://localhost:{Config.WEB_PORT}")
        print(f"   Network access: http://{local_ip}:{Config.WEB_PORT}")

        # Start Discord client
        print("\nStarting Discord client...")
        print("   Waiting for connection...")

        # Run client in main thread
        asyncio.run(run_discord_bot())

    except KeyboardInterrupt:
        print("\n\nStopping application...")
        logger.info("Application stopped by user")
    except Exception as e:
        print(f"\nCRITICAL ERROR: {e}")
        logger.error(f"Critical error: {e}", exc_info=True)
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"Unexpected error: {e}")
        input("Press Enter to exit...")
