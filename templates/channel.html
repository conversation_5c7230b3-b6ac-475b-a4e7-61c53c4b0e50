{% extends "base.html" %}

{% block title %}{{ channel.name }} - Discord Logger{% endblock %}
{% block page_title %}
    {% if channel.guild_name %}
        {{ channel.guild_name }} / #{{ channel.name }}
    {% else %}
        {{ channel.name }}
    {% endif %}
{% endblock %}

{% block header %}
<div class="channel-header">
    <div class="channel-info">
        <h1>
            {% if channel.type == 'dm' %}
                <i class="fas fa-user"></i>
            {% else %}
                <i class="fas fa-hashtag"></i>
            {% endif %}
            {% if channel.guild_name %}
                {{ channel.guild_name }} / {{ channel.name }}
            {% else %}
                {{ channel.name }}
            {% endif %}
        </h1>
        {% if channel.topic %}
        <p class="channel-topic">{{ channel.topic }}</p>
        {% endif %}
    </div>
    
    <div class="channel-controls">
        <div class="toggle-deleted">
            <label>
                <input type="checkbox" id="includeDeleted" {% if include_deleted %}checked{% endif %}>
                Показать удаленные
            </label>
        </div>
        
        <div class="pagination-info">
            Страница {{ page }}
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="messages-container" id="messagesContainer">
    {% if messages %}
        {% for message in messages %}
        <div class="message {% if message.deleted %}deleted{% endif %} {% if message.bot %}bot-message{% endif %}" 
             data-message-id="{{ message.id }}">
            
            <div class="message-avatar">
                {% if message.avatar_url %}
                <img src="{{ message.avatar_url }}" alt="{{ message.username }}" class="avatar">
                {% else %}
                <div class="avatar-placeholder">{{ message.username[0].upper() }}</div>
                {% endif %}
            </div>
            
            <div class="message-content">
                <div class="message-header">
                    <span class="username {% if message.bot %}bot{% endif %}">
                        {{ message.display_name or message.username }}
                        {% if message.bot %}<span class="bot-tag">БОТ</span>{% endif %}
                    </span>
                    <span class="timestamp">{{ message.timestamp|datetime }}</span>
                    {% if message.edited_timestamp %}
                    <span class="edited">(изменено {{ message.edited_timestamp|datetime }})</span>
                    {% endif %}
                    {% if message.deleted %}
                    <span class="deleted-tag">УДАЛЕНО {{ message.deleted_timestamp|datetime }}</span>
                    {% endif %}
                </div>
                
                <div class="message-body">
                    {% if message.content %}
                    <div class="message-text">{{ message.content }}</div>
                    {% endif %}
                    
                    {% if message.attachments %}
                    <div class="message-attachments">
                        {% for attachment in message.attachments %}
                        <div class="attachment">
                            <i class="fas fa-paperclip"></i>
                            <a href="{{ attachment.url }}" target="_blank">{{ attachment.filename }}</a>
                            <span class="file-size">({{ (attachment.size / 1024) | round(1) }} KB)</span>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    {% if message.embeds %}
                    <div class="message-embeds">
                        {% for embed in message.embeds %}
                        <div class="embed">
                            {% if embed.title %}
                            <div class="embed-title">{{ embed.title }}</div>
                            {% endif %}
                            {% if embed.description %}
                            <div class="embed-description">{{ embed.description }}</div>
                            {% endif %}
                            {% if embed.url %}
                            <a href="{{ embed.url }}" target="_blank" class="embed-link">{{ embed.url }}</a>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    {% else %}
        <div class="no-messages">
            <i class="fas fa-comment-slash"></i>
            <p>В этом канале пока нет сообщений</p>
        </div>
    {% endif %}
</div>

<div class="pagination">
    {% if page > 1 %}
    <a href="{{ url_for('view_channel', channel_id=channel.id, page=page-1, include_deleted=include_deleted) }}" 
       class="pagination-btn">
        <i class="fas fa-chevron-left"></i> Предыдущая
    </a>
    {% endif %}
    
    <span class="pagination-current">Страница {{ page }}</span>
    
    {% if messages|length == per_page %}
    <a href="{{ url_for('view_channel', channel_id=channel.id, page=page+1, include_deleted=include_deleted) }}" 
       class="pagination-btn">
        Следующая <i class="fas fa-chevron-right"></i>
    </a>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const includeDeletedCheckbox = document.getElementById('includeDeleted');
    
    includeDeletedCheckbox.addEventListener('change', function() {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('include_deleted', this.checked ? 'true' : 'false');
        currentUrl.searchParams.set('page', '1'); // Сброс на первую страницу
        window.location.href = currentUrl.toString();
    });
    
    // Автоматическая прокрутка к последнему сообщению
    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer && {{ page }} === 1) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    // Подсветка упоминаний и ссылок
    const messageTexts = document.querySelectorAll('.message-text');
    messageTexts.forEach(function(messageText) {
        let content = messageText.innerHTML;
        
        // Подсветка упоминаний пользователей
        content = content.replace(/@(\w+)/g, '<span class="mention">@$1</span>');
        
        // Подсветка ссылок
        content = content.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="message-link">$1</a>');
        
        messageText.innerHTML = content;
    });
});

// Функция для загрузки новых сообщений (можно расширить для real-time обновлений)
function loadNewMessages() {
    // Здесь можно добавить AJAX запрос для получения новых сообщений
    // и обновления страницы без перезагрузки
}
</script>
{% endblock %}
