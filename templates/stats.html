{% extends "base.html" %}

{% block title %}Статистика - Discord Logger{% endblock %}
{% block page_title %}Статистика логирования{% endblock %}

{% block content %}
<div class="stats-container">
    <div class="stats-overview">
        <h2><i class="fas fa-chart-line"></i> Общая статистика</h2>
        
        <div class="stats-grid">
            <div class="stat-card large">
                <div class="stat-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ "{:,}".format(stats.total_messages) }}</div>
                    <div class="stat-label">Всего сообщений</div>
                </div>
            </div>
            
            <div class="stat-card large">
                <div class="stat-icon deleted">
                    <i class="fas fa-trash"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ "{:,}".format(stats.deleted_messages) }}</div>
                    <div class="stat-label">Удаленных сообщений</div>
                    <div class="stat-percentage">
                        {% if stats.total_messages > 0 %}
                        ({{ "%.1f"|format((stats.deleted_messages / stats.total_messages) * 100) }}%)
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ stats.total_guilds }}</div>
                    <div class="stat-label">Серверов</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-hashtag"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ stats.total_channels }}</div>
                    <div class="stat-label">Каналов</div>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number">{{ stats.total_users }}</div>
                    <div class="stat-label">Пользователей</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="stats-details">
        <div class="stats-section">
            <h3><i class="fas fa-fire"></i> Топ каналов по активности</h3>
            <div class="top-list">
                {% for channel in stats.top_channels %}
                <div class="top-item">
                    <div class="item-info">
                        <div class="item-name">
                            <i class="fas fa-hashtag"></i>
                            {{ channel[0] }}
                        </div>
                        <div class="item-meta">
                            {% if channel[1] %}
                            <span class="guild-name">{{ channel[1] }}</span>
                            {% else %}
                            <span class="guild-name">Личные сообщения</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="item-count">
                        {{ "{:,}".format(channel[2]) }} сообщений
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="stats-section">
            <h3><i class="fas fa-crown"></i> Топ пользователей по активности</h3>
            <div class="top-list">
                {% for user in stats.top_users %}
                <div class="top-item">
                    <div class="item-info">
                        <div class="item-name">
                            <i class="fas fa-user"></i>
                            {{ user[1] or user[0] }}
                        </div>
                        {% if user[1] and user[1] != user[0] %}
                        <div class="item-meta">
                            <span class="username">@{{ user[0] }}</span>
                        </div>
                        {% endif %}
                    </div>
                    <div class="item-count">
                        {{ "{:,}".format(user[2]) }} сообщений
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="stats-info">
        <div class="info-card">
            <h3><i class="fas fa-info-circle"></i> О статистике</h3>
            <ul>
                <li>Статистика обновляется в реальном времени</li>
                <li>Учитываются все сообщения, включая удаленные</li>
                <li>Боты исключены из топа пользователей</li>
                <li>DM каналы учитываются отдельно</li>
            </ul>
        </div>
        
        <div class="info-card">
            <h3><i class="fas fa-database"></i> База данных</h3>
            <p>Все данные хранятся локально в SQLite базе данных. 
               Никакая информация не передается на внешние серверы.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Анимация чисел при загрузке страницы
    const statNumbers = document.querySelectorAll('.stat-number');
    
    statNumbers.forEach(function(element) {
        const finalValue = parseInt(element.textContent.replace(/,/g, ''));
        if (isNaN(finalValue)) return;
        
        let currentValue = 0;
        const increment = Math.ceil(finalValue / 50);
        const timer = setInterval(function() {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            element.textContent = currentValue.toLocaleString();
        }, 30);
    });
});
</script>
{% endblock %}
