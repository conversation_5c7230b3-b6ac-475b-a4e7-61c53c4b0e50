<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Discord Logger{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app">
        <!-- Боковая панель -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-archive"></i> Discord Logger</h2>
            </div>
            
            <div class="sidebar-content">
                <div class="nav-section">
                    <a href="{{ url_for('index') }}" class="nav-item {% if request.endpoint == 'index' %}active{% endif %}">
                        <i class="fas fa-home"></i> Главная
                    </a>
                    <a href="{{ url_for('search') }}" class="nav-item {% if request.endpoint == 'search' %}active{% endif %}">
                        <i class="fas fa-search"></i> Поиск
                    </a>
                    <a href="{{ url_for('stats') }}" class="nav-item {% if request.endpoint == 'stats' %}active{% endif %}">
                        <i class="fas fa-chart-bar"></i> Статистика
                    </a>
                </div>
                
                {% block sidebar %}{% endblock %}
            </div>
        </div>
        
        <!-- Основной контент -->
        <div class="main-content">
            <div class="content-header">
                {% block header %}
                <h1>{% block page_title %}Discord Logger{% endblock %}</h1>
                {% endblock %}
            </div>
            
            <div class="content-body">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    
    <script src="{{ url_for('static', filename='script.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
