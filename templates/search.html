{% extends "base.html" %}

{% block title %}Поиск - Discord Logger{% endblock %}
{% block page_title %}Поиск сообщений{% endblock %}

{% block content %}
<div class="search-container">
    <div class="search-form">
        <form method="GET" action="{{ url_for('search') }}">
            <div class="search-input-group">
                <input type="text" name="q" value="{{ query }}" placeholder="Введите текст для поиска..." 
                       class="search-input" autofocus>
                <button type="submit" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>
    
    {% if query %}
    <div class="search-results">
        <div class="results-header">
            <h3>Результаты поиска для "{{ query }}"</h3>
            <span class="results-count">Найдено: {{ messages|length }} сообщений</span>
        </div>
        
        {% if messages %}
        <div class="messages-list">
            {% for message in messages %}
            <div class="search-result-message {% if message.deleted %}deleted{% endif %}">
                <div class="message-meta">
                    <div class="channel-info">
                        <i class="fas fa-hashtag"></i>
                        <span class="guild-name">{{ message.guild_name or 'Личные сообщения' }}</span>
                        {% if message.guild_name %}
                        <span class="separator">/</span>
                        <span class="channel-name">{{ message.channel_name }}</span>
                        {% endif %}
                    </div>
                    <div class="message-timestamp">{{ message.timestamp|datetime }}</div>
                </div>
                
                <div class="message-preview">
                    <div class="message-author">
                        {% if message.avatar_url %}
                        <img src="{{ message.avatar_url }}" alt="{{ message.username }}" class="author-avatar">
                        {% else %}
                        <div class="author-avatar-placeholder">{{ message.username[0].upper() }}</div>
                        {% endif %}
                        <span class="author-name {% if message.bot %}bot{% endif %}">
                            {{ message.display_name or message.username }}
                            {% if message.bot %}<span class="bot-tag">БОТ</span>{% endif %}
                        </span>
                        {% if message.deleted %}
                        <span class="deleted-indicator">УДАЛЕНО</span>
                        {% endif %}
                    </div>
                    
                    <div class="message-content-preview">
                        {{ message.content[:200] }}{% if message.content|length > 200 %}...{% endif %}
                    </div>
                </div>
                
                <div class="message-actions">
                    <a href="{{ url_for('view_channel', channel_id=message.channel_id) }}#message-{{ message.id }}" 
                       class="view-in-channel">
                        <i class="fas fa-external-link-alt"></i>
                        Перейти к сообщению
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-results">
            <i class="fas fa-search"></i>
            <h3>Ничего не найдено</h3>
            <p>Попробуйте изменить поисковый запрос</p>
        </div>
        {% endif %}
    </div>
    {% else %}
    <div class="search-tips">
        <h3><i class="fas fa-lightbulb"></i> Советы по поиску</h3>
        <ul>
            <li>Используйте ключевые слова из сообщений</li>
            <li>Поиск работает по содержимому сообщений</li>
            <li>Поиск учитывает удаленные сообщения</li>
            <li>Регистр букв не важен</li>
        </ul>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Подсветка найденного текста
    const query = "{{ query }}";
    if (query) {
        const messageContents = document.querySelectorAll('.message-content-preview');
        messageContents.forEach(function(content) {
            const regex = new RegExp(`(${query})`, 'gi');
            content.innerHTML = content.innerHTML.replace(regex, '<mark>$1</mark>');
        });
    }
    
    // Автофокус на поле поиска
    const searchInput = document.querySelector('.search-input');
    if (searchInput && !query) {
        searchInput.focus();
    }
});
</script>
{% endblock %}
