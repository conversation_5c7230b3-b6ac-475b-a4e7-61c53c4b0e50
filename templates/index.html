{% extends "base.html" %}

{% block title %}Главная - Discord Logger{% endblock %}
{% block page_title %}Серверы и каналы{% endblock %}

{% block sidebar %}
<div class="servers-list">
    <!-- DM каналы -->
    {% if dm_channels %}
    <div class="server-section">
        <div class="server-header">
            <i class="fas fa-user"></i>
            <span>Личные сообщения</span>
        </div>
        <div class="channels-list">
            {% for channel in dm_channels %}
            <a href="{{ url_for('view_channel', channel_id=channel.id) }}" class="channel-item dm-channel">
                <i class="fas fa-user"></i>
                <span>{{ channel.name }}</span>
            </a>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Серверы -->
    {% for guild_id, guild_data in guilds_with_channels.items() %}
    <div class="server-section">
        <div class="server-header" onclick="toggleServer({{ guild_id }})">
            {% if guild_data.info.icon_url %}
            <img src="{{ guild_data.info.icon_url }}" alt="{{ guild_data.info.name }}" class="server-icon">
            {% else %}
            <div class="server-icon-placeholder">{{ guild_data.info.name[0].upper() }}</div>
            {% endif %}
            <span>{{ guild_data.info.name }}</span>
            <i class="fas fa-chevron-down toggle-icon" id="toggle-{{ guild_id }}"></i>
        </div>
        
        <div class="channels-list" id="channels-{{ guild_id }}">
            {% for channel in guild_data.channels %}
            <a href="{{ url_for('view_channel', channel_id=channel.id) }}" class="channel-item">
                {% if channel.type == 'text' %}
                <i class="fas fa-hashtag"></i>
                {% elif channel.type == 'voice' %}
                <i class="fas fa-volume-up"></i>
                {% elif channel.type == 'category' %}
                <i class="fas fa-folder"></i>
                {% else %}
                <i class="fas fa-comment"></i>
                {% endif %}
                <span>{{ channel.name }}</span>
            </a>
            {% endfor %}
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block content %}
<div class="welcome-content">
    <div class="welcome-card">
        <h2><i class="fas fa-archive"></i> Добро пожаловать в Discord Logger</h2>
        <p>Этот инструмент позволяет просматривать логи сообщений Discord, включая удаленные сообщения.</p>
        
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-server"></i>
                <div class="stat-info">
                    <span class="stat-number">{{ guilds_with_channels|length }}</span>
                    <span class="stat-label">Серверов</span>
                </div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-hashtag"></i>
                <div class="stat-info">
                    <span class="stat-number">
                        {% set total_channels = 0 %}
                        {% for guild_id, guild_data in guilds_with_channels.items() %}
                            {% set total_channels = total_channels + guild_data.channels|length %}
                        {% endfor %}
                        {{ total_channels + dm_channels|length }}
                    </span>
                    <span class="stat-label">Каналов</span>
                </div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-user"></i>
                <div class="stat-info">
                    <span class="stat-number">{{ dm_channels|length }}</span>
                    <span class="stat-label">DM каналов</span>
                </div>
            </div>
        </div>
        
        <div class="quick-actions">
            <a href="{{ url_for('search') }}" class="action-btn">
                <i class="fas fa-search"></i>
                Поиск сообщений
            </a>
            <a href="{{ url_for('stats') }}" class="action-btn">
                <i class="fas fa-chart-bar"></i>
                Статистика
            </a>
        </div>
    </div>
    
    <div class="instructions-card">
        <h3><i class="fas fa-info-circle"></i> Как использовать</h3>
        <ul>
            <li><strong>Выберите канал</strong> из списка слева для просмотра сообщений</li>
            <li><strong>Используйте поиск</strong> для быстрого нахождения нужных сообщений</li>
            <li><strong>Удаленные сообщения</strong> отмечены специальным образом</li>
            <li><strong>Статистика</strong> показывает общую информацию о логах</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function toggleServer(guildId) {
    const channelsList = document.getElementById(`channels-${guildId}`);
    const toggleIcon = document.getElementById(`toggle-${guildId}`);
    
    if (channelsList.style.display === 'none') {
        channelsList.style.display = 'block';
        toggleIcon.style.transform = 'rotate(0deg)';
    } else {
        channelsList.style.display = 'none';
        toggleIcon.style.transform = 'rotate(-90deg)';
    }
}

// Сохранение состояния свернутых серверов
document.addEventListener('DOMContentLoaded', function() {
    // Можно добавить логику для сохранения состояния в localStorage
});
</script>
{% endblock %}
