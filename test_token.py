#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Discord Token Tester - Проверка токена Discord
"""

import asyncio
import discord
import sys

async def test_token():
    """Test Discord token"""
    print("Discord Token Tester")
    print("=" * 40)
    
    token = input("Enter your Discord token: ").strip()
    
    if not token:
        print("ERROR: Token cannot be empty!")
        return
    
    print("\nTesting token...")
    
    try:
        # Create client with minimal intents
        intents = discord.Intents.default()
        client = discord.Client(intents=intents)
        
        @client.event
        async def on_ready():
            print(f"SUCCESS: Token is valid!")
            print(f"Logged in as: {client.user}")
            print(f"User ID: {client.user.id}")
            print(f"Connected to {len(client.guilds)} servers")
            
            # List servers
            if client.guilds:
                print("\nServers:")
                for guild in client.guilds[:5]:  # Show first 5 servers
                    print(f"  - {guild.name} ({guild.id})")
                if len(client.guilds) > 5:
                    print(f"  ... and {len(client.guilds) - 5} more")
            
            await client.close()
        
        # Try to login
        await client.start(token)
        
    except discord.LoginFailure:
        print("ERROR: Invalid token!")
        print("\nMake sure you're using a USER token, not a BOT token.")
        print("User tokens start with 'mfa.' or are long strings of letters/numbers.")
        
    except Exception as e:
        print(f"ERROR: {e}")
    
    print("\nTest completed.")

if __name__ == "__main__":
    try:
        asyncio.run(test_token())
    except KeyboardInterrupt:
        print("\nTest cancelled.")
    except Exception as e:
        print(f"Unexpected error: {e}")
    
    input("Press Enter to exit...")
