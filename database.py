import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional
import asyncio
import threading

class DatabaseManager:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """Инициализация базы данных"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Таблица серверов
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS guilds (
                    id INTEGER PRIMARY KEY,
                    name TEXT NOT NULL,
                    icon_url TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Таблица каналов
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY,
                    guild_id INTEGER,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    topic TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (guild_id) REFERENCES guilds (id)
                )
            ''')
            
            # Таблица пользователей
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    username TEXT NOT NULL,
                    display_name TEXT,
                    avatar_url TEXT,
                    bot BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Таблица сообщений
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY,
                    channel_id INTEGER NOT NULL,
                    user_id INTEGER NOT NULL,
                    content TEXT,
                    embeds TEXT,
                    attachments TEXT,
                    timestamp TIMESTAMP NOT NULL,
                    edited_timestamp TIMESTAMP,
                    deleted BOOLEAN DEFAULT FALSE,
                    deleted_timestamp TIMESTAMP,
                    message_type TEXT DEFAULT 'default',
                    reply_to INTEGER,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (reply_to) REFERENCES messages (id)
                )
            ''')
            
            # Индексы для быстрого поиска
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_channel ON messages(channel_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_user ON messages(user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages(timestamp)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_deleted ON messages(deleted)')
            
            conn.commit()
    
    def save_guild(self, guild_id: int, name: str, icon_url: str = None):
        """Сохранить информацию о сервере"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO guilds (id, name, icon_url)
                    VALUES (?, ?, ?)
                ''', (guild_id, name, icon_url))
                conn.commit()
    
    def save_channel(self, channel_id: int, guild_id: int, name: str, 
                    channel_type: str, topic: str = None):
        """Сохранить информацию о канале"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO channels (id, guild_id, name, type, topic)
                    VALUES (?, ?, ?, ?, ?)
                ''', (channel_id, guild_id, name, channel_type, topic))
                conn.commit()
    
    def save_user(self, user_id: int, username: str, display_name: str = None,
                 avatar_url: str = None, bot: bool = False):
        """Сохранить информацию о пользователе"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO users (id, username, display_name, avatar_url, bot)
                    VALUES (?, ?, ?, ?, ?)
                ''', (user_id, username, display_name, avatar_url, bot))
                conn.commit()
    
    def save_message(self, message_id: int, channel_id: int, user_id: int,
                    content: str, embeds: List = None, attachments: List = None,
                    timestamp: datetime = None, message_type: str = 'default',
                    reply_to: int = None):
        """Сохранить сообщение"""
        if timestamp is None:
            timestamp = datetime.now()
        
        embeds_json = json.dumps(embeds) if embeds else None
        attachments_json = json.dumps(attachments) if attachments else None
        
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO messages 
                    (id, channel_id, user_id, content, embeds, attachments, 
                     timestamp, message_type, reply_to)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (message_id, channel_id, user_id, content, embeds_json,
                      attachments_json, timestamp, message_type, reply_to))
                conn.commit()
    
    def mark_message_deleted(self, message_id: int):
        """Отметить сообщение как удаленное"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE messages 
                    SET deleted = TRUE, deleted_timestamp = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (message_id,))
                conn.commit()
    
    def update_message(self, message_id: int, content: str):
        """Обновить содержимое сообщения"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE messages 
                    SET content = ?, edited_timestamp = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (content, message_id))
                conn.commit()
    
    def get_guilds(self) -> List[Dict]:
        """Получить список серверов"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM guilds ORDER BY name')
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def get_channels(self, guild_id: int = None) -> List[Dict]:
        """Получить список каналов"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if guild_id:
                cursor.execute('''
                    SELECT c.*, g.name as guild_name 
                    FROM channels c 
                    LEFT JOIN guilds g ON c.guild_id = g.id 
                    WHERE c.guild_id = ? 
                    ORDER BY c.name
                ''', (guild_id,))
            else:
                cursor.execute('''
                    SELECT c.*, g.name as guild_name 
                    FROM channels c 
                    LEFT JOIN guilds g ON c.guild_id = g.id 
                    ORDER BY g.name, c.name
                ''')
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    def get_messages(self, channel_id: int, limit: int = 100, 
                    offset: int = 0, include_deleted: bool = True) -> List[Dict]:
        """Получить сообщения канала"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            where_clause = "WHERE m.channel_id = ?"
            params = [channel_id]
            
            if not include_deleted:
                where_clause += " AND m.deleted = FALSE"
            
            cursor.execute(f'''
                SELECT m.*, u.username, u.display_name, u.avatar_url, u.bot,
                       c.name as channel_name, g.name as guild_name
                FROM messages m
                JOIN users u ON m.user_id = u.id
                JOIN channels c ON m.channel_id = c.id
                LEFT JOIN guilds g ON c.guild_id = g.id
                {where_clause}
                ORDER BY m.timestamp DESC
                LIMIT ? OFFSET ?
            ''', params + [limit, offset])
            
            columns = [description[0] for description in cursor.description]
            messages = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Парсим JSON поля
            for msg in messages:
                if msg['embeds']:
                    msg['embeds'] = json.loads(msg['embeds'])
                if msg['attachments']:
                    msg['attachments'] = json.loads(msg['attachments'])
            
            return messages
    
    def search_messages(self, query: str, limit: int = 100) -> List[Dict]:
        """Поиск сообщений"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT m.*, u.username, u.display_name, u.avatar_url,
                       c.name as channel_name, g.name as guild_name
                FROM messages m
                JOIN users u ON m.user_id = u.id
                JOIN channels c ON m.channel_id = c.id
                LEFT JOIN guilds g ON c.guild_id = g.id
                WHERE m.content LIKE ?
                ORDER BY m.timestamp DESC
                LIMIT ?
            ''', (f'%{query}%', limit))
            
            columns = [description[0] for description in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]
