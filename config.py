import os

# Попытка загрузить dotenv, если доступен
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Если dotenv не установлен, продолжаем без него
    pass

class Config:
    # Discord settings
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN', '')
    
    # Database settings
    DATABASE_PATH = 'discord_logs.db'
    
    # Web app settings
    WEB_HOST = '0.0.0.0'  # Доступ из локальной сети
    WEB_PORT = 5000
    WEB_DEBUG = True
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    MAX_MESSAGE_LENGTH = 4000
    
    @classmethod
    def get_token(cls):
        """Get token from environment or ask user"""
        if cls.DISCORD_TOKEN:
            return cls.DISCORD_TOKEN

        print("\n" + "="*60)
        print("DISCORD TOKEN SETUP")
        print("="*60)
        print("This program needs your Discord USER token (NOT bot token).")
        print("\nMethod 1 - Browser Console:")
        print("1. Open Discord in browser (discord.com)")
        print("2. Press F12 -> Console tab")
        print("3. Paste this code and press Enter:")
        print("   (webpackChunkdiscord_app.push([[''],{},e=>{m=[];")
        print("   for(let c in e.c)m.push(e.c[c])}]),m).find(m=>")
        print("   m?.exports?.default?.getToken!==void 0)")
        print("   .exports.default.getToken()")
        print("\nMethod 2 - Network Tab:")
        print("1. Open Discord in browser (discord.com)")
        print("2. Press F12 -> Network tab")
        print("3. Refresh page (F5)")
        print("4. Find any API request (contains 'api/v9')")
        print("5. In headers find 'authorization' - copy the value")
        print("\nWARNING: Never share your token with anyone!")
        print("="*60)

        token = input("\nEnter your Discord token: ").strip()
        if not token:
            raise ValueError("Token cannot be empty!")

        # Save to .env file for future runs
        try:
            with open('.env', 'w', encoding='utf-8') as f:
                f.write(f'DISCORD_TOKEN={token}\n')
        except:
            pass  # If can't save, continue without saving

        cls.DISCORD_TOKEN = token
        return token
