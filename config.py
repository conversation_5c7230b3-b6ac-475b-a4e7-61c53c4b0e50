import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Discord settings
    DISCORD_TOKEN = os.getenv('DISCORD_TOKEN', '')
    
    # Database settings
    DATABASE_PATH = 'discord_logs.db'
    
    # Web app settings
    WEB_HOST = '0.0.0.0'  # Доступ из локальной сети
    WEB_PORT = 5000
    WEB_DEBUG = True
    
    # Logging settings
    LOG_LEVEL = 'INFO'
    MAX_MESSAGE_LENGTH = 4000
    
    @classmethod
    def get_token(cls):
        """Получить токен из переменной окружения или запросить у пользователя"""
        if cls.DISCORD_TOKEN:
            return cls.DISCORD_TOKEN
        
        token = input("Введите Discord Bot Token: ").strip()
        if not token:
            raise ValueError("Токен не может быть пустым!")
        
        # Сохраняем в .env файл для будущих запусков
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(f'DISCORD_TOKEN={token}\n')
        
        cls.DISCORD_TOKEN = token
        return token
