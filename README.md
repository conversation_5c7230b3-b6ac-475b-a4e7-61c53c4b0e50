# Discord Logger

Быстрое логирование сообщений Discord с веб-интерфейсом для просмотра, включая удаленные сообщения.

## 🚀 Возможности

- **Быстрое логирование** - Сохраняет все сообщения в реальном времени
- **Удаленные сообщения** - Сохраняет сообщения даже после их удаления
- **Веб-интерфейс** - Красивый интерфейс в стиле Discord
- **Поиск** - Быстрый поиск по всем сообщениям
- **Статистика** - Подробная аналитика активности
- **Локальная сеть** - Доступ с любого устройства в сети
- **Безопасность** - Все данные хранятся локально

## 📋 Требования

- Python 3.7+
- Discord Bot Token

## 🛠️ Установка

1. **Скачайте проект**
   - Скачайте все файлы в одну папку
   - Убедитесь, что все файлы находятся в одной директории

2. **Установите зависимости**

   **Автоматическая установка:**
   ```bash
   python install.py
   ```

   **Ручная установка:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Создайте Discord приложение**
   - Перейдите на https://discord.com/developers/applications
   - Нажмите "New Application"
   - Дайте название приложению

4. **Создайте бота**
   - Перейдите в раздел "Bot"
   - Нажмите "Add Bot"
   - Скопируйте токен бота

5. **Настройте права бота**
   - В разделе "Bot" включите все Privileged Gateway Intents:
     - Presence Intent
     - Server Members Intent
     - Message Content Intent
   
6. **Пригласите бота на сервер**
   - В разделе "OAuth2 > URL Generator":
     - Выберите scope: "bot"
     - Выберите права: 
       - "Read Messages/View Channels"
       - "Read Message History"
   - Используйте сгенерированную ссылку для приглашения

## 🚀 Запуск

```bash
python main.py
```

При первом запуске введите токен вашего Discord бота.

## 🌐 Веб-интерфейс

После запуска веб-интерфейс будет доступен по адресам:
- **Локально**: http://localhost:5000
- **В сети**: http://[IP_ВАШЕГО_ПК]:5000

### Основные функции веб-интерфейса:

- **Главная страница** - Список серверов и каналов
- **Просмотр каналов** - Все сообщения канала с пагинацией
- **Поиск** - Поиск по содержимому сообщений
- **Статистика** - Общая статистика и топы активности

## 📁 Структура проекта

```
discord-logger/
├── main.py              # Основной файл запуска
├── discord_logger.py    # Discord бот для логирования
├── web_app.py          # Flask веб-приложение
├── database.py         # Работа с базой данных
├── config.py           # Конфигурация
├── requirements.txt    # Зависимости Python
├── templates/          # HTML шаблоны
│   ├── base.html
│   ├── index.html
│   ├── channel.html
│   ├── search.html
│   └── stats.html
└── static/            # Статические файлы
    ├── style.css      # CSS стили
    └── script.js      # JavaScript
```

## 🔧 Конфигурация

Основные настройки находятся в файле `config.py`:

- `WEB_HOST` - IP адрес веб-сервера (по умолчанию 0.0.0.0 для доступа из сети)
- `WEB_PORT` - Порт веб-сервера (по умолчанию 5000)
- `DATABASE_PATH` - Путь к файлу базы данных SQLite
- `LOG_LEVEL` - Уровень логирования

## 💾 База данных

Приложение использует SQLite базу данных для хранения:
- Информации о серверах и каналах
- Сообщений пользователей
- Метаданных (вложения, embeds)
- Истории изменений и удалений

## 🔒 Безопасность

- Все данные хранятся локально
- Токен бота сохраняется в файле `.env`
- Никакая информация не передается на внешние серверы
- Веб-интерфейс доступен только в локальной сети

## ⚠️ Важные замечания

1. **Токен бота** - Это секретная информация! Не делитесь им и не публикуйте в открытом доступе.

2. **Права доступа** - Бот может логировать только те каналы, к которым у него есть доступ.

3. **Производительность** - При большом количестве сообщений рекомендуется периодически очищать старые логи.

4. **Соответствие ToS** - Убедитесь, что использование соответствует Terms of Service Discord и законодательству вашей страны.

## 🐛 Устранение неполадок

### Бот не подключается
- Проверьте правильность токена
- Убедитесь, что бот приглашен на сервер
- Проверьте права бота

### Веб-интерфейс недоступен
- Проверьте, что порт 5000 не занят
- Убедитесь, что файрвол не блокирует соединение
- Попробуйте другой порт в настройках

### Сообщения не логируются
- Проверьте права бота на чтение сообщений
- Убедитесь, что включены Privileged Gateway Intents
- Проверьте логи на наличие ошибок

## 📝 Логи

Логи приложения сохраняются в файл `discord_logger.log` и выводятся в консоль.

## 🤝 Поддержка

При возникновении проблем:
1. Проверьте логи приложения
2. Убедитесь в правильности настройки бота
3. Проверьте права доступа

## 📄 Лицензия

Этот проект предназначен для образовательных целей. Используйте ответственно и в соответствии с правилами Discord и применимым законодательством.
